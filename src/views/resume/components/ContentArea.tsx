'use client';

import { useEffect, useRef } from 'react';
import { ResizablePanelGroup, ResizablePanel } from "@/components/ui/resizable";
import { CustomResizableHandle } from "@/components/CustomResizableHandle";
import ModuleManager from '@/components/ModuleManager';
import Toolbar from '@/components/Toolbar';
import GlobalLoading from '@/components/GlobalLoading';
import FullScreenLoading from '@/components/FullScreenLoading';
import ResumeArea from '@/components/ResumeArea';
import SmartOnePageRollback from '@/components/SmartOnePageRollback';
import { useResumeStyleInitializer } from "@/hooks/useResumeStyleInitializer";
import { useModuleStore } from '@/store/useModuleStore';
import { useSmartOnePageStore } from '@/store/useSmartOnePageStore';
import { ResumeDetailResponse } from '@/api/server/types/resume';

// 扩展PageTransitionEvent类型定义
declare global {
  interface PageTransitionEvent extends Event {
    persisted: boolean;
  }
}

interface ContentAreaProps {
  resumeDetail?: ResumeDetailResponse;
  activeModule?: string;
}


/**
 * 内容区域组件
 * 包含左侧模块管理和右侧简历预览
 */
export default function ContentArea({ resumeDetail, activeModule }: ContentAreaProps) {
  // 使用Hook初始化简历样式
  useResumeStyleInitializer();

  // 获取 store 中的初始化方法和相关状态
  const initializeFromResumeDetail = useModuleStore(state => state.initializeFromResumeDetail);
  const isInitializing = useModuleStore(state => state.isInitializing);
  const modules = useModuleStore(state => state.modules);
  const setActiveModule = useModuleStore(state => state.setActiveModule);

  // 智能一页状态
  const isSmartOnePageProcessing = useSmartOnePageStore(state => state.isProcessing);

  const toolbarRef = useRef<HTMLDivElement>(null);

  // 判断是否需要显示 loading：正在初始化 或者 还没有模块数据
  const shouldShowLoading = isInitializing || Object.keys(modules).length === 0;



  // 当 resumeDetail 存在时，初始化 store 数据
  useEffect(() => {
    console.log('🔄 ContentArea useEffect - resumeDetail changed:', {
      hasResumeDetail: !!resumeDetail,
      resumeId: resumeDetail?.id,
      resumeName: resumeDetail?.resume_name,
      updatedAt: resumeDetail?.updated_at,
      hasInitializeFunction: !!initializeFromResumeDetail
    });

    if (resumeDetail && initializeFromResumeDetail) {
      console.log('✅ 正在初始化 store 数据...');
      initializeFromResumeDetail(resumeDetail);
    }
  }, [resumeDetail, initializeFromResumeDetail]);

  // 处理URL中的activeModule参数，在数据初始化完成后设置活跃模块
  useEffect(() => {
    if (activeModule && !isInitializing && setActiveModule) {
      // 延迟一点时间确保模块数据已经完全加载
      const timer = setTimeout(() => {
        setActiveModule(activeModule);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [activeModule, isInitializing, setActiveModule]);

  // 处理浏览器前进/后退缓存问题
  useEffect(() => {
    console.log('🎯 设置浏览器事件监听器...');

    const handlePageShow = (event: PageTransitionEvent) => {
      console.log('📄 pageshow 事件触发:', {
        persisted: event.persisted,
        hasResumeDetail: !!resumeDetail,
        resumeId: resumeDetail?.id,
        timestamp: new Date().toISOString()
      });

      // 如果页面是从缓存中恢复的，重新初始化数据
      if (event.persisted && resumeDetail && initializeFromResumeDetail) {
        console.log('🔄 页面从缓存恢复，重新初始化数据');
        initializeFromResumeDetail(resumeDetail);
      }
    };

    const handleVisibilityChange = () => {
      console.log('👁️ visibilitychange 事件触发:', {
        hidden: document.hidden,
        visibilityState: document.visibilityState,
        hasResumeDetail: !!resumeDetail,
        timestamp: new Date().toISOString()
      });

      // 当页面重新变为可见时，重新初始化数据
      if (!document.hidden && resumeDetail && initializeFromResumeDetail) {
        console.log('🔄 页面重新可见，重新初始化数据');
        initializeFromResumeDetail(resumeDetail);
      }
    };

    const handleFocus = () => {
      console.log('🎯 focus 事件触发:', {
        hasResumeDetail: !!resumeDetail,
        timestamp: new Date().toISOString()
      });

      // 当窗口重新获得焦点时，重新初始化数据
      if (resumeDetail && initializeFromResumeDetail) {
        console.log('🔄 窗口重新获得焦点，重新初始化数据');
        initializeFromResumeDetail(resumeDetail);
      }
    };

    const handlePopState = (event: PopStateEvent) => {
      console.log('⬅️ popstate 事件触发 (浏览器前进/后退):', {
        state: event.state,
        hasResumeDetail: !!resumeDetail,
        timestamp: new Date().toISOString()
      });

      // 浏览器前进/后退时，重新初始化数据
      if (resumeDetail && initializeFromResumeDetail) {
        console.log('🔄 浏览器前进/后退，重新初始化数据');
        setTimeout(() => {
          initializeFromResumeDetail(resumeDetail);
        }, 100); // 稍微延迟一下确保页面状态稳定
      }
    };

    // 监听各种事件
    window.addEventListener('pageshow', handlePageShow);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('popstate', handlePopState);

    console.log('✅ 事件监听器已设置');

    return () => {
      console.log('🧹 清理事件监听器');
      window.removeEventListener('pageshow', handlePageShow);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('popstate', handlePopState);
    };
  }, [resumeDetail, initializeFromResumeDetail]);



  return (
    <>
      {/* 全局 Loading 效果 */}
      <GlobalLoading
        isLoading={shouldShowLoading}
        message="正在初始化简历数据..."
      />

      {/* 智能一页 Loading 效果 */}
      <FullScreenLoading
        isVisible={isSmartOnePageProcessing}
        message="智能一页调整中..."
      />

      {/* 智能一页回滚按钮 */}
      <SmartOnePageRollback />

      <div className="flex-1">
        <div className="container-custom">
          <ResizablePanelGroup
            direction="horizontal"
            className="min-h-[calc(100vh-56px-2rem)]"
          >
            {/* 左侧模块管理 - 占据剩余宽度 */}
            <ResizablePanel defaultSize={54} minSize={30}>
              <div className="h-full w-full flex justify-center">
                <div className="w-full" style={{ maxWidth: '100%' }}>
                  <ModuleManager className="h-full sticky top-8" />
                </div>
              </div>
            </ResizablePanel>

            {/* 可调整大小的分隔线 */}
            <CustomResizableHandle className='w-3 bg-white hover:bg-gray-400 transition-colors duration-200' />

            {/* 右侧区域 - 动态宽度 */}
            <ResizablePanel defaultSize={46} minSize={30}>
              <div className="h-full w-full flex flex-col items-center justify-start py-4 shadow-md rounded-md "  style={{ backgroundColor: '#f1f1fb' }}>
                {/* 容器 - 占满父级容器宽度 */}
                <div className="flex flex-col justify-start items-center w-full px-4">
                  <div ref={toolbarRef} className="w-full mb-4">
                    <Toolbar className="rounded-lg" />
                  </div>
                  <div
                    className="w-full overflow-y-scroll hide-scrollbar"
                    style={{
                      height: 'calc(100vh - 56px - 2rem - 2rem - 44px - 1rem)' // 减去Toolbar高度(44px) + mb-4(16px)
                    }}
                  >
                    <ResumeArea containerRef={toolbarRef} />
                  </div>
                </div>
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      </div>
    </>
  );
}
