import { create } from 'zustand';
import { ResumeDetailResponse } from '@/api/server/types/resume';
import { resumeApi } from '@/api/client/resume';
import { SaveResumeRequest } from '@/api/client/types/resume';
import { useResumeStyleStore } from './useResumeStyleStore';

// 定义带标签的字段类型
export interface FieldWithLabel<T = string> {
  label: string;
  value: T;
}

// 定义一个通用的模块项目类型
export type ModuleItemType =
  | BasicInfoItem
  | EducationItem[]
  | WorkItem[]
  | ProjectItem[]
  | ResearchItem[]
  | TeamItem[]
  | PortfolioItem[]
  | OtherItem[]
  | PersonalSummaryItem
  | HonorsItem
  | SkillsItem
  | CustomItem[]
  | Record<string, unknown>;

// 自定义字段类型
interface CustomField {
  id: string;
  label: string;
  value: string;
}

// 基本信息模块数据类型
export interface BasicInfoItem {
  avatar: FieldWithLabel<string>;
  avatar_filter: FieldWithLabel<string>;
  birth: FieldWithLabel<string>;
  birth_type: FieldWithLabel<string>;
  city: FieldWithLabel<string>;
  created_at: FieldWithLabel<number>;
  customize_fields: FieldWithLabel<CustomField[]>;
  email: FieldWithLabel<string>;
  ethnicity: FieldWithLabel<string>;
  gender: FieldWithLabel<string>;
  gitee: FieldWithLabel<string>;
  github: FieldWithLabel<string>;
  height: FieldWithLabel<string>;
  id: FieldWithLabel<number>;
  intended_city: FieldWithLabel<string>;
  job: FieldWithLabel<string>;
  job_status: FieldWithLabel<string>;
  marital: FieldWithLabel<string>;
  max_salary: FieldWithLabel<string>;
  name: FieldWithLabel<string>;
  origin: FieldWithLabel<string>;
  phone: FieldWithLabel<string>;
  political_affiliation: FieldWithLabel<string>;
  site: FieldWithLabel<string>;
  updated_at: FieldWithLabel<number>;
  wechat: FieldWithLabel<string>;
  weight: FieldWithLabel<string>;
  [key: string]: unknown;
}

// 教育经历项目类型
export interface EducationItem {
  id: string;
  school_name: FieldWithLabel<string>;
  college_name: FieldWithLabel<string>;
  major: FieldWithLabel<string>;
  degree: FieldWithLabel<string>;
  city?: FieldWithLabel<string>;
  start_date: FieldWithLabel<string>;
  end_date: FieldWithLabel<string>;
  description: FieldWithLabel<string>;
  school_tags: FieldWithLabel<string[]>;
  index: number;
}

// 工作经历项目类型
export interface WorkItem {
  id: string;
  company: FieldWithLabel<string>;
  department: FieldWithLabel<string>;
  city: FieldWithLabel<string>;
  job: FieldWithLabel<string>;
  start_month: FieldWithLabel<string>;
  end_month: FieldWithLabel<string>;
  desc: FieldWithLabel<string>;
  company_tags: FieldWithLabel<string[]>;
  job_tags: FieldWithLabel<string[]>;
  index: number;
}

// 项目经历项目类型
export interface ProjectItem {
  id: string;
  name: FieldWithLabel<string>;
  role: FieldWithLabel<string>;
  company: FieldWithLabel<string>;
  start_month: FieldWithLabel<string>;
  end_month: FieldWithLabel<string>;
  desc: FieldWithLabel<string>;
  index: number;
}

// 研究经历项目类型
export interface ResearchItem {
  id: string;
  name: FieldWithLabel<string>;
  role: FieldWithLabel<string>;
  department: FieldWithLabel<string>;
  city: FieldWithLabel<string>;
  start_month: FieldWithLabel<string>;
  end_month: FieldWithLabel<string>;
  desc: FieldWithLabel<string>;
  index: number;
}

// 社团经历项目类型
export interface TeamItem {
  id: string;
  name: FieldWithLabel<string>;
  department: FieldWithLabel<string>;
  role: FieldWithLabel<string>;
  city: FieldWithLabel<string>;
  start_month: FieldWithLabel<string>;
  end_month: FieldWithLabel<string>;
  desc: FieldWithLabel<string>;
  index: number;
}

// 作品集项目类型
export interface PortfolioItem {
  id: string;
  name: FieldWithLabel<string>;
  url: FieldWithLabel<string>;
  index: number;
}

// 其他项目类型
export interface OtherItem {
  id: string;
  name: FieldWithLabel<string>;
  desc: FieldWithLabel<string>;
  index: number;
}

// 个人总结项目类型
export interface PersonalSummaryItem {
  summary: FieldWithLabel<string>;
}

// 荣誉项目类型
export interface HonorValueItem {
  name: FieldWithLabel<string>;
  index: number;
  desc: FieldWithLabel<string>;
}

// 荣誉墙数据类型
export interface HonorsItem {
  honorWallLayout: FieldWithLabel<string>;
  honorWallStyle: FieldWithLabel<string>;
  values: FieldWithLabel<HonorValueItem[]>;
}

// 技能项目类型
export interface SkillValueItem {
  name: FieldWithLabel<string>;
  index: number;
  score: FieldWithLabel<number>;
  desc: FieldWithLabel<string>;
}

// 技能条数据类型
export interface SkillsItem {
  skillLayout: FieldWithLabel<string>;
  skillStyle: FieldWithLabel<string>;
  values: FieldWithLabel<SkillValueItem[]>;
}

// 自定义模块项目类型
export interface CustomItem {
  id: string;
  name: FieldWithLabel<string>;
  role: FieldWithLabel<string>;
  start_month: FieldWithLabel<string>;
  end_month: FieldWithLabel<string>;
  desc: FieldWithLabel<string>;
  index: number;
}

// 自定义模块类型
export interface CustomModule {
  id: string;
  name: string;
  index: number;
  items: CustomItem[];
}

export interface Module {
  id: string;
  name: string;
  type: string;
  is_visible: boolean;
  is_required?: boolean; // 是否为必需模块，不可删除
  is_custom?: boolean; // 是否为自定义模块
  support_ai?: boolean; // 是否支持AI功能
  index: number; // 显示顺序
  item?: ModuleItemType; // 存储表单字段数据，可以是对象或数组
}

interface ModuleState {
  modules: Record<string, Module>; // 改为对象，key为id，值为模块对象
  customModules: CustomModule[]; // 自定义模块数组
  activeModuleId: string | null;
  activeIndex: number[]; // 当前激活模块的索引数组
  showModuleDropdown: boolean; // 是否显示模块管理下拉菜单
  hiddenModules: Module[]; // 被移动到"新增模块"区域的模块
  resumeName: string; // 简历名称
  lastUpdatedAt: number; // 最近更新时间戳
  isInitializing: boolean; // 是否正在初始化数据

  // 简历基本信息字段
  resumeId: number | null; // 简历ID
  userId: number | null; // 用户ID
  templateId: number | null; // 模板ID
  previewImageUrl: string; // 预览图片URL
  createdAt: string; // 创建时间
  componentName: string; // 组件名称

  // Slogan 字段
  slogan: {
    slogan: FieldWithLabel<string>;
    title: FieldWithLabel<string>;
  } | null;

  setActiveModule: (id: string | null) => void;
  setActiveIndex: (index: number[]) => void; // 设置当前激活的索引数组
  toggleModuleDropdown: () => void; // 切换模块管理下拉菜单显示状态
  addCustomModule: (name: string) => void; // 添加自定义模块
  editModule: (id: string, name: string) => void; // 编辑模块名称
  deleteModule: (id: string) => void; // 删除模块
  reorderModules: (sourceIndex: number, destinationIndex: number) => void; // 重新排序模块
  restoreModule: (id: string) => void; // 恢复被移动到"新增模块"区域的模块
  updateModuleItem: (id: string, item: ModuleItemType) => void; // 更新模块的item数据
  updateCustomModuleItem: (id: string, items: CustomItem[]) => void; // 更新自定义模块的items数据
  updateSlogan: (sloganData: { title: FieldWithLabel<string>; slogan: FieldWithLabel<string> }) => void; // 更新slogan数据
  updateResumeName: (name: string) => Promise<void>; // 更新简历名称
  updateLastUpdatedAt: () => void; // 更新最近更新时间
  initializeFromResumeDetail: (resumeDetail: ResumeDetailResponse) => void; // 从简历详情初始化数据
  saveResume: () => Promise<void>; // 保存简历到后端
}

// 创建模块状态管理
export const useModuleStore = create<ModuleState>()((set) => ({
      modules: {},
      customModules: [],
      hiddenModules: [], // 初始化为空数组
      activeModuleId: null, // 默认不选择任何模块，等待数据初始化
      activeIndex: [], // 默认不选择任何索引，等待数据初始化
      showModuleDropdown: false,
      resumeName: '我的简历', // 默认简历名称
      lastUpdatedAt: Date.now(), // 初始化为当前时间戳
      isInitializing: true, // 初始化为 true，页面加载时显示 loading

      // 简历基本信息字段初始值
      resumeId: null,
      userId: null,
      templateId: null,
      previewImageUrl: '',
      createdAt: '',
      componentName: '',

      // Slogan 字段初始值
      slogan: null,

      setActiveModule: (id: string | null) => set({ activeModuleId: id }),
      setActiveIndex: (index: number[]) => set({ activeIndex: index }),
      toggleModuleDropdown: () => set((state) => ({
        showModuleDropdown: !state.showModuleDropdown
      })),
      addCustomModule: (name: string) => set((state) => {
        // 检查是否已达到最大自定义模块数量限制（10个）
        if (state.customModules.length >= 10) {
          return state;
        }

        // 生成随机ID
        const randomId = `custom-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

        // 获取当前自定义模块数量，用于生成名称
        const customModuleCount = state.customModules.length + 1;

        // 创建新的名称（如果提供了名称则使用，否则生成默认名称）
        const moduleName = name || `自定义模块${customModuleCount}`;

        // 获取当前最大的 index 值（包括modules和customModules）
        const moduleIndexes = Object.values(state.modules).map(module => module.index);
        const customModuleIndexes = state.customModules.map(cm => cm.index);
        const maxIndex = Math.max(...moduleIndexes, ...customModuleIndexes, -1);

        // 创建新的自定义模块，添加到customModules数组中
        const newCustomModule: CustomModule = {
          id: randomId,
          name: moduleName,
          index: maxIndex + 1,
          items: [] // 空的items数组
        };

        return {
          customModules: [...state.customModules, newCustomModule]
        };
      }),
      editModule: (id: string, name: string) => set((state) => {
        // 首先检查是否是 customModules 中的模块
        const customModuleIndex = state.customModules.findIndex(cm => cm.id === id);

        if (customModuleIndex !== -1) {
          // 如果是 customModules 中的模块，更新其名称
          const updatedCustomModules = [...state.customModules];
          updatedCustomModules[customModuleIndex] = {
            ...updatedCustomModules[customModuleIndex],
            name: name
          };

          return {
            customModules: updatedCustomModules
          };
        }

        // 如果是传统的 modules 中的模块，更新其名称
        return {
          modules: {
            ...state.modules,
            [id]: { ...state.modules[id], name }
          }
        };
      }),
      deleteModule: (id: string) => set((state) => {
        // 首先检查是否是 customModules 中的模块
        const customModuleIndex = state.customModules.findIndex(cm => cm.id === id);

        if (customModuleIndex !== -1) {
          // 如果是 customModules 中的模块，先清空数据再删除
          const updatedCustomModules = [...state.customModules];
          // 先清空该自定义模块的数据
          updatedCustomModules[customModuleIndex] = {
            ...updatedCustomModules[customModuleIndex],
            items: [] // 清空自定义模块的items数据
          };

          // 然后删除该模块
          const finalCustomModules = updatedCustomModules.filter(cm => cm.id !== id);

          return {
            customModules: finalCustomModules,
            // 如果当前激活的模块被删除，则设置为null
            activeModuleId: state.activeModuleId === id ? null : state.activeModuleId,
            // 更新最后修改时间
            lastUpdatedAt: Date.now()
          };
        }

        // 查找要删除的模块（在传统的 modules 中）
        const moduleToDelete = state.modules[id];

        // 如果是必需模块或者找不到模块，则不做任何操作
        if (!moduleToDelete || moduleToDelete.is_required) {
          return state;
        }

        // 获取模块的默认空数据
        const getEmptyModuleData = (moduleId: string) => {
          switch (moduleId) {
            case 'education':
            case 'work':
            case 'project':
            case 'research':
            case 'team':
            case 'portfolio':
            case 'other':
              return []; // 数组类型模块返回空数组
            case 'personal_summary':
              return { summary: { label: '', value: '' } };
            case 'honors':
              return {
                honorWallLayout: { label: '', value: '' },
                honorWallStyle: { label: '', value: '' },
                values: { label: '', value: [] }
              };
            case 'skills':
              return {
                skillLayout: { label: '', value: '' },
                skillStyle: { label: '', value: '' },
                values: { label: '', value: [] }
              };
            default:
              return {};
          }
        };

        // 对于已有模块，先清空数据再设置为不可见
        return {
          modules: {
            ...state.modules,
            [id]: {
              ...moduleToDelete,
              is_visible: false,
              item: getEmptyModuleData(id) // 清空模块数据
            }
          },
          // 如果当前激活的模块被删除，则设置为null
          activeModuleId: state.activeModuleId === id ? null : state.activeModuleId,
          // 更新最后修改时间
          lastUpdatedAt: Date.now()
        };
      }),

      // 恢复被设置为不可见的模块
      restoreModule: (id: string) => set((state) => {
        // 查找要恢复的模块
        const moduleToRestore = state.modules[id];

        // 如果找不到模块或模块已经可见，则不做任何操作
        if (!moduleToRestore || moduleToRestore.is_visible) {
          return state;
        }

        // 获取当前最大的 index 值
        const maxIndex = Math.max(...Object.values(state.modules).filter(m => m.is_visible).map(module => module.index), -1);

        // 恢复模块，设置为可见并更新 index
        return {
          modules: {
            ...state.modules,
            [id]: {
              ...moduleToRestore,
              is_visible: true,
              index: maxIndex + 1
            }
          }
        };
      }),

      reorderModules: (sourceIndex: number, destinationIndex: number) => set((state) => {
        // 合并 modules 和 customModules，创建统一的模块列表
        const allModules = [
          // 添加传统的 modules（只显示 is_visible: true 的）
          ...Object.values(state.modules).filter(module => module.is_visible),
          // 添加新的 customModules（转换为 Module 格式，只显示有值的自定义模块）
          ...state.customModules
            .filter(customModule => customModule.items && customModule.items.length > 0)
            .map(customModule => ({
              id: customModule.id,
              name: customModule.name,
              type: 'custom' as const,
              is_visible: true,
              is_custom: true,
              is_required: false,
              support_ai: false,
              index: customModule.index,
              item: customModule.items
            }))
        ];

        // 按照 index 排序
        const sortedModules = allModules.sort((a, b) => a.index - b.index);

        // 检查索引是否有效
        if (sourceIndex < 0 || sourceIndex >= sortedModules.length ||
            destinationIndex < 0 || destinationIndex >= sortedModules.length) {
          return state;
        }

        // 确保基本信息模块始终在第一位
        if (sortedModules[sourceIndex].id === 'basic_info' || sortedModules[destinationIndex].id === 'basic_info') {
          return state;
        }

        // 移动模块
        const newModules = [...sortedModules];
        const [movedModule] = newModules.splice(sourceIndex, 1);
        newModules.splice(destinationIndex, 0, movedModule);

        // 更新所有模块的 index 属性
        const updatedModules = newModules.map((module, idx) => ({
          ...module,
          index: idx
        }));

        // 分离更新后的模块：传统模块和自定义模块
        const updatedTraditionalModules: Record<string, Module> = { ...state.modules };
        const updatedCustomModules: CustomModule[] = [];

        updatedModules.forEach(module => {
          if (module.is_custom && !state.modules[module.id]) {
            // 这是一个 customModule
            updatedCustomModules.push({
              id: module.id,
              name: module.name,
              index: module.index,
              items: module.item as CustomItem[]
            });
          } else {
            // 这是一个传统的 module
            updatedTraditionalModules[module.id] = module;
          }
        });

        // 保持 customModules 的原始顺序，只更新 index
        const finalCustomModules = state.customModules.map(cm => {
          const updated = updatedCustomModules.find(ucm => ucm.id === cm.id);
          return updated || cm;
        });

        return {
          modules: updatedTraditionalModules,
          customModules: finalCustomModules
        };
      }),

      // 更新模块的item数据
      updateModuleItem: (id: string, item: ModuleItemType) => set((state) => {
        // 查找要更新的模块
        const moduleToUpdate = state.modules[id];

        // 如果找不到模块，则不做任何操作
        if (!moduleToUpdate) {
          return state;
        }

        // 更新指定模块的item数据和最近更新时间
        return {
          modules: {
            ...state.modules,
            [id]: {
              ...moduleToUpdate,
              item: item
            }
          },
          lastUpdatedAt: Date.now() // 自动更新最近更新时间
        };
      }),

      // 更新自定义模块的items数据
      updateCustomModuleItem: (id: string, items: CustomItem[]) => set((state) => {
        // 查找要更新的自定义模块
        const customModuleIndex = state.customModules.findIndex(module => module.id === id);

        // 如果找不到自定义模块，则不做任何操作
        if (customModuleIndex === -1) {
          return state;
        }

        // 更新指定自定义模块的items数据和最近更新时间
        const updatedCustomModules = [...state.customModules];
        updatedCustomModules[customModuleIndex] = {
          ...updatedCustomModules[customModuleIndex],
          items: items
        };

        return {
          customModules: updatedCustomModules,
          lastUpdatedAt: Date.now() // 自动更新最近更新时间
        };
      }),

      // 更新slogan数据
      updateSlogan: (sloganData: { title: FieldWithLabel<string>; slogan: FieldWithLabel<string> }) => set(() => ({
        slogan: sloganData,
        lastUpdatedAt: Date.now() // 自动更新最近更新时间
      })),

      // 更新简历名称
      updateResumeName: async (name: string) => {
        const state = useModuleStore.getState();

        if (!state.resumeId) {
          throw new Error('简历ID不存在');
        }

        try {
          // 调用API更新简历名称
          const response = await resumeApi.updateResumeName(state.resumeId.toString(), name);

          // API调用成功后只更新简历名称，不更新lastUpdatedAt
          set({
            resumeName: response.resume_name
          });
        } catch (error) {
          throw error;
        }
      },

      // 更新最近更新时间
      updateLastUpdatedAt: () => set({
        lastUpdatedAt: Date.now()
      }),

      // 从简历详情初始化数据
      initializeFromResumeDetail: (resumeDetail: ResumeDetailResponse) => {
        console.log('🚀 initializeFromResumeDetail 被调用:', {
          resumeId: resumeDetail.id,
          resumeName: resumeDetail.resume_name,
          updatedAt: resumeDetail.updated_at,
          timestamp: new Date().toISOString()
        });

        // 使用 setTimeout 来确保 loading 状态能被正确显示
        setTimeout(() => {
          console.log('⏰ 开始初始化 store 数据...');
          set((state) => {

        // 转换 API 数据到 store 格式的辅助函数
        const convertApiFieldToStoreField = (apiField: unknown) => {
          if (apiField && typeof apiField === 'object' && apiField !== null && 'label' in apiField && 'value' in apiField) {
            return apiField as FieldWithLabel;
          }
          // 如果不是预期格式，返回默认结构
          return { label: '', value: apiField || '' };
        };

        // 转换基本信息
        const convertBasicInfo = (apiBasicInfo: unknown) => {
          if (!apiBasicInfo || typeof apiBasicInfo !== 'object' || apiBasicInfo === null || !('item' in apiBasicInfo)) {
            return {};
          }

          const apiItem = (apiBasicInfo as { item: Record<string, unknown> }).item;
          return {
            avatar: convertApiFieldToStoreField(apiItem.avatar),
            avatar_filter: convertApiFieldToStoreField(apiItem.avatar_filter),
            birth: convertApiFieldToStoreField(apiItem.birth),
            birth_type: convertApiFieldToStoreField(apiItem.birth_type),
            city: convertApiFieldToStoreField(apiItem.city),
            created_at: convertApiFieldToStoreField(apiItem.created_at),
            customize_fields: convertApiFieldToStoreField(apiItem.customize_fields),
            email: convertApiFieldToStoreField(apiItem.email),
            ethnicity: convertApiFieldToStoreField(apiItem.ethnicity),
            gender: convertApiFieldToStoreField(apiItem.gender),
            gitee: convertApiFieldToStoreField(apiItem.gitee),
            github: convertApiFieldToStoreField(apiItem.github),
            height: convertApiFieldToStoreField(apiItem.height),
            id: convertApiFieldToStoreField(apiItem.id),
            intended_city: convertApiFieldToStoreField(apiItem.intended_city),
            job: convertApiFieldToStoreField(apiItem.job),
            job_status: convertApiFieldToStoreField(apiItem.job_status),
            marital: convertApiFieldToStoreField(apiItem.marital),
            max_salary: convertApiFieldToStoreField(apiItem.max_salary),
            name: convertApiFieldToStoreField(apiItem.name),
            origin: convertApiFieldToStoreField(apiItem.origin),
            phone: convertApiFieldToStoreField(apiItem.phone),
            political_affiliation: convertApiFieldToStoreField(apiItem.political_affiliation),
            site: convertApiFieldToStoreField(apiItem.site),
            updated_at: convertApiFieldToStoreField(apiItem.updated_at),
            wechat: convertApiFieldToStoreField(apiItem.wechat),
            weight: convertApiFieldToStoreField(apiItem.weight),
          };
        };

        // 转换数组类型模块（教育、工作等）
        const convertArrayModule = (apiModule: unknown) => {
          if (!apiModule || typeof apiModule !== 'object' || apiModule === null || !('item' in apiModule) || !Array.isArray((apiModule as { item: unknown }).item)) {
            return [];
          }

          const items = (apiModule as { item: Record<string, unknown>[] }).item;
          return items.map((item: Record<string, unknown>, index: number) => {
            const convertedItem: Record<string, unknown> = {
              index: item.index || index  // 保持 index 为数字类型
            };

            // 转换所有字段，但保持 id 和 index 为原始类型
            Object.keys(item).forEach(key => {
              if (key === 'id') {
                // id 字段保持为字符串类型
                convertedItem[key] = item[key] || `item-${index}`;
              } else if (key !== 'index') {
                // 其他字段转换为 FieldWithLabel 格式
                convertedItem[key] = convertApiFieldToStoreField(item[key]);
              }
            });

            return convertedItem;
          });
        };

        // 转换单个对象类型模块（个人总结、技能等）
        const convertObjectModule = (apiModule: unknown) => {
          if (!apiModule || typeof apiModule !== 'object' || apiModule === null || !('item' in apiModule)) {
            return {};
          }

          const item = (apiModule as { item: Record<string, unknown> }).item;
          const convertedItem: Record<string, unknown> = {};
          Object.keys(item).forEach(key => {
            convertedItem[key] = convertApiFieldToStoreField(item[key]);
          });

          return convertedItem;
        };

        // 更新模块数据
        const updatedModules = { ...state.modules };

        // 创建基本信息模块
        if (resumeDetail.basic_info) {
          updatedModules.basic_info = {
            id: 'basic_info',
            name: resumeDetail.basic_info.name || '基本信息',
            type: 'basic',
            is_visible: resumeDetail.basic_info.is_visible,
            is_required: true,
            support_ai: false,
            index: resumeDetail.basic_info.index || 0,
            item: convertBasicInfo(resumeDetail.basic_info)
          };
        }

        // 创建教育经历模块
        if (resumeDetail.education) {
          updatedModules.education = {
            id: 'education',
            name: resumeDetail.education.name || '教育经历',
            type: 'education',
            is_visible: resumeDetail.education.is_visible,
            support_ai: true,
            index: resumeDetail.education.index || 1,
            item: convertArrayModule(resumeDetail.education) as unknown as EducationItem[]
          };
        }

        // 创建工作经历模块
        if (resumeDetail.work) {
          updatedModules.work = {
            id: 'work',
            name: resumeDetail.work.name || '工作经历',
            type: 'work',
            is_visible: resumeDetail.work.is_visible,
            support_ai: true,
            index: resumeDetail.work.index || 2,
            item: convertArrayModule(resumeDetail.work) as unknown as WorkItem[]
          };
        }

        // 创建项目经历模块
        if (resumeDetail.project) {
          updatedModules.project = {
            id: 'project',
            name: resumeDetail.project.name || '项目经历',
            type: 'project',
            is_visible: resumeDetail.project.is_visible,
            support_ai: true,
            index: resumeDetail.project.index || 3,
            item: convertArrayModule(resumeDetail.project) as unknown as ProjectItem[]
          };
        }

        // 创建研究经历模块
        if (resumeDetail.research) {
          updatedModules.research = {
            id: 'research',
            name: resumeDetail.research.name || '研究经历',
            type: 'research',
            is_visible: resumeDetail.research.is_visible,
            support_ai: true,
            index: resumeDetail.research.index || 4,
            item: convertArrayModule(resumeDetail.research) as unknown as ResearchItem[]
          };
        }

        // 创建社团经历模块
        if (resumeDetail.team) {
          updatedModules.team = {
            id: 'team',
            name: resumeDetail.team.name || '社团经历',
            type: 'team',
            is_visible: resumeDetail.team.is_visible,
            support_ai: true,
            index: resumeDetail.team.index || 5,
            item: convertArrayModule(resumeDetail.team) as unknown as TeamItem[]
          };
        }

        // 创建作品集模块
        if (resumeDetail.portfolio) {
          updatedModules.portfolio = {
            id: 'portfolio',
            name: resumeDetail.portfolio.name || '作品集',
            type: 'portfolio',
            is_visible: resumeDetail.portfolio.is_visible,
            support_ai: false,
            index: resumeDetail.portfolio.index || 6,
            item: convertArrayModule(resumeDetail.portfolio) as unknown as PortfolioItem[]
          };
        }

        // 创建其他信息模块
        if (resumeDetail.other) {
          updatedModules.other = {
            id: 'other',
            name: resumeDetail.other.name || '其他',
            type: 'other',
            is_visible: resumeDetail.other.is_visible,
            support_ai: false,
            index: resumeDetail.other.index || 7,
            item: convertArrayModule(resumeDetail.other) as unknown as OtherItem[]
          };
        }

        // 创建个人总结模块
        if (resumeDetail.personal_summary) {
          updatedModules.personal_summary = {
            id: 'personal_summary',
            name: resumeDetail.personal_summary.name || '个人总结',
            type: 'summary',
            is_visible: resumeDetail.personal_summary.is_visible,
            support_ai: true,
            index: resumeDetail.personal_summary.index || 8,
            item: convertObjectModule(resumeDetail.personal_summary)
          };
        }

        // 创建荣誉栏模块
        if (resumeDetail.honors) {
          updatedModules.honors = {
            id: 'honors',
            name: resumeDetail.honors.name || '荣誉栏',
            type: 'honors',
            is_visible: resumeDetail.honors.is_visible,
            support_ai: false,
            index: resumeDetail.honors.index || 9,
            item: convertObjectModule(resumeDetail.honors)
          };
        }

        // 创建技能条模块
        if (resumeDetail.skills) {
          updatedModules.skills = {
            id: 'skills',
            name: resumeDetail.skills.name || '技能条',
            type: 'skills',
            is_visible: resumeDetail.skills.is_visible,
            support_ai: false,
            index: resumeDetail.skills.index || 10,
            item: convertObjectModule(resumeDetail.skills)
          };
        }

        // 转换自定义模块
        const convertedCustomModules: CustomModule[] = [];
        if (resumeDetail.custom_modules && Array.isArray(resumeDetail.custom_modules)) {
          resumeDetail.custom_modules.forEach((customModule, index) => {
            if (customModule && typeof customModule === 'object' && 'id' in customModule && 'name' in customModule) {
              const convertedItems: CustomItem[] = [];

              // 转换自定义模块的 items
              if ('items' in customModule && Array.isArray(customModule.items)) {
                customModule.items.forEach((item: unknown, itemIndex: number) => {
                  if (item && typeof item === 'object') {
                    const itemObj = item as Record<string, unknown>;
                    const convertedItem: CustomItem = {
                      id: (itemObj.id as string) || `custom-item-${itemIndex + 1}`,
                      index: itemIndex,
                      name: convertApiFieldToStoreField(itemObj.name) as FieldWithLabel<string>,
                      role: convertApiFieldToStoreField(itemObj.role) as FieldWithLabel<string>,
                      start_month: convertApiFieldToStoreField(itemObj.start_month) as FieldWithLabel<string>,
                      end_month: convertApiFieldToStoreField(itemObj.end_month) as FieldWithLabel<string>,
                      desc: convertApiFieldToStoreField(itemObj.desc) as FieldWithLabel<string>
                    };
                    convertedItems.push(convertedItem);
                  }
                });
              }

              convertedCustomModules.push({
                id: customModule.id as string,
                name: customModule.name as string,
                index: (customModule.index as number) || (100 + index), // 自定义模块从100开始编号
                items: convertedItems
              });
            }
          });
        }

        // 转换slogan数据
        let convertedSlogan = null;
        if (resumeDetail.slogan) {
          convertedSlogan = {
            slogan: convertApiFieldToStoreField(resumeDetail.slogan.slogan) as FieldWithLabel<string>,
            title: convertApiFieldToStoreField(resumeDetail.slogan.title) as FieldWithLabel<string>
          };
        }

        // 初始化简历样式数据
        if (resumeDetail.resume_style) {
          const styleData = resumeDetail.resume_style;

          // 批量更新样式数据到 useResumeStyleStore
          const resumeStyleStore = useResumeStyleStore.getState();

          // 定义可更新的样式字段类型
          type UpdatableStyleKeys = Exclude<keyof typeof resumeStyleStore, 'updateStyle' | 'applyStylesToDOM'>;

          // 逐个更新样式字段
          Object.entries(styleData).forEach(([key, value]) => {
            if (key in resumeStyleStore && key !== 'updateStyle' && key !== 'applyStylesToDOM') {
              resumeStyleStore.updateStyle(key as UpdatableStyleKeys, value as never);
            }
          });
        }


        // 设置默认的 activeModuleId（选择第一个可见的模块）
        let defaultActiveModuleId = null;
        const allVisibleModules = Object.values(updatedModules)
          .filter(module => module.is_visible)
          .sort((a, b) => a.index - b.index);

        if (allVisibleModules.length > 0) {
          defaultActiveModuleId = allVisibleModules[0].id;
        }

            const newState = {
              modules: updatedModules,
              customModules: convertedCustomModules, // 更新自定义模块
              activeModuleId: defaultActiveModuleId, // 设置默认选中的模块
              resumeName: resumeDetail.resume_name || '我的简历',
              lastUpdatedAt: new Date(resumeDetail.updated_at).getTime() || Date.now(),
              isInitializing: false, // 初始化完成，清除 loading 状态
              // 更新简历基本信息字段
              resumeId: resumeDetail.id,
              userId: resumeDetail.user_id,
              templateId: resumeDetail.template_id,
              previewImageUrl: resumeDetail.preview_image_url || '',
              createdAt: resumeDetail.created_at,
              componentName: resumeDetail.component_name || '',
              // 更新slogan字段
              slogan: convertedSlogan
            };

            console.log('✅ store 数据初始化完成:', {
              resumeId: newState.resumeId,
              resumeName: newState.resumeName,
              lastUpdatedAt: new Date(newState.lastUpdatedAt).toISOString(),
              modulesCount: Object.keys(newState.modules).length,
              customModulesCount: newState.customModules.length,
              activeModuleId: newState.activeModuleId
            });

            return newState;
          });
        }, 100); // 100ms 延迟，确保 loading 状态能被看到
      },

      // 保存简历到后端
      saveResume: async () => {
        const state = useModuleStore.getState();

        if (!state.resumeId) {
          throw new Error('简历ID不存在');
        }

        // 计算完整度评分
        const calculateCompletenessScore = () => {
          // 检查字段是否有效
          const isFieldValid = (field: unknown): boolean => {
            if (!field || typeof field !== 'object') return false;
            const fieldObj = field as { value?: unknown };
            return fieldObj.value !== undefined && fieldObj.value !== null && fieldObj.value !== '';
          };

          // 评分基本信息
          const scoreBasicInfo = () => {
            const basicInfo = state.modules.basic_info?.item;
            if (!basicInfo || typeof basicInfo !== 'object') return 0;

            const basicInfoObj = basicInfo as Record<string, unknown>;
            const requiredFields = ['name', 'phone', 'email', 'intended_city', 'job_status'];
            let score = 0;

            requiredFields.forEach(field => {
              if (isFieldValid(basicInfoObj[field])) {
                score += 5;
              }
            });

            // 检查期望薪资
            if (isFieldValid(basicInfoObj.max_salary)) {
              score += 5;
            }

            return score; // 最大30分
          };

          // 评分教育经历
          const scoreEducation = () => {
            const items = state.modules.education?.item;
            if (!items || !Array.isArray(items) || items.length === 0) return 0;

            const hasCompleteRecord = items.some((item: unknown) => {
              if (!item || typeof item !== 'object') return false;
              const itemObj = item as Record<string, unknown>;
              return ['school_name', 'major', 'degree', 'start_date', 'end_date'].every(field =>
                isFieldValid(itemObj[field])
              );
            });

            return hasCompleteRecord ? 20 : 10; // 最大20分
          };

          // 评分工作经历
          const scoreWork = () => {
            const items = state.modules.work?.item;
            if (!items || !Array.isArray(items) || items.length === 0) return 0;

            const hasCompleteRecord = items.some((item: unknown) => {
              if (!item || typeof item !== 'object') return false;
              const itemObj = item as Record<string, unknown>;
              return ['company', 'job', 'start_month', 'end_month'].every(field =>
                isFieldValid(itemObj[field])
              );
            });

            return hasCompleteRecord ? 20 : 10; // 最大20分
          };

          // 评分个人总结
          const scorePersonalSummary = () => {
            const item = state.modules.personal_summary?.item;
            if (!item || typeof item !== 'object') return 0;

            const itemObj = item as Record<string, unknown>;
            return isFieldValid(itemObj.summary) ? 10 : 0; // 最大10分
          };

          // 计算总分
          const basicInfoScore = scoreBasicInfo();
          const educationScore = scoreEducation();
          const workScore = scoreWork();
          const personalSummaryScore = scorePersonalSummary();

          const totalScore = basicInfoScore + educationScore + workScore + personalSummaryScore;
          const maxScore = 80; // 30 + 20 + 20 + 10

          const percentage = Math.round((totalScore / maxScore) * 100);
          return `${Math.max(0, Math.min(100, percentage))}%`;
        };

        // 将store数据转换为API格式
        const convertStoreDataToApiFormat = () => {
          // 转换基本信息
          const basic_info = {
            id: 'basic_info',
            index: state.modules.basic_info?.index || 0,
            is_required: true,
            is_visible: state.modules.basic_info?.is_visible ?? true,
            item: state.modules.basic_info?.item || {},
            name: state.modules.basic_info?.name || '基本信息',
            support_ai: false,
            type: 'basic'
          };

          // 转换教育经历
          const education = {
            id: 'education',
            index: state.modules.education?.index || 1,
            is_visible: state.modules.education?.is_visible ?? true,
            item: state.modules.education?.item || [],
            name: state.modules.education?.name || '教育经历',
            support_ai: true,
            type: 'education'
          };

          // 转换工作经历
          const work = {
            id: 'work',
            index: state.modules.work?.index || 2,
            is_visible: state.modules.work?.is_visible ?? true,
            item: state.modules.work?.item || [],
            name: state.modules.work?.name || '工作经历',
            support_ai: true,
            type: 'work'
          };

          // 转换项目经历
          const project = {
            id: 'project',
            index: state.modules.project?.index || 3,
            is_visible: state.modules.project?.is_visible ?? true,
            item: state.modules.project?.item || [],
            name: state.modules.project?.name || '项目经历',
            support_ai: true,
            type: 'project'
          };

          // 转换技能专长
          const skills = {
            id: 'skills',
            index: state.modules.skills?.index || 4,
            is_visible: state.modules.skills?.is_visible ?? true,
            item: state.modules.skills?.item || { skillLayout: { label: '', value: '' }, skillStyle: { label: '', value: '' }, values: { label: '', value: [] } },
            name: state.modules.skills?.name || '技能专长',
            support_ai: true,
            type: 'skills'
          };

          // 转换荣誉奖项
          const honors = {
            id: 'honors',
            index: state.modules.honors?.index || 5,
            is_visible: state.modules.honors?.is_visible ?? true,
            item: state.modules.honors?.item || { honorWallLayout: { label: '', value: '' }, honorWallStyle: { label: '', value: '' }, values: { label: '', value: [] } },
            name: state.modules.honors?.name || '荣誉奖项',
            support_ai: true,
            type: 'honors'
          };

          // 转换个人总结
          const personal_summary = {
            id: 'personal_summary',
            index: state.modules.personal_summary?.index || 6,
            is_visible: state.modules.personal_summary?.is_visible ?? true,
            item: state.modules.personal_summary?.item || { summary: { label: '', value: '' } },
            name: state.modules.personal_summary?.name || '个人总结',
            support_ai: true,
            type: 'personal_summary'
          };

          // 转换作品集
          const portfolio = {
            id: 'portfolio',
            index: state.modules.portfolio?.index || 7,
            is_visible: state.modules.portfolio?.is_visible ?? true,
            item: state.modules.portfolio?.item || [],
            name: state.modules.portfolio?.name || '作品集',
            support_ai: true,
            type: 'portfolio'
          };

          // 转换科研经历
          const research = {
            id: 'research',
            index: state.modules.research?.index || 8,
            is_visible: state.modules.research?.is_visible ?? true,
            item: state.modules.research?.item || [],
            name: state.modules.research?.name || '科研经历',
            support_ai: true,
            type: 'research'
          };

          // 转换团队经历
          const team = {
            id: 'team',
            index: state.modules.team?.index || 9,
            is_visible: state.modules.team?.is_visible ?? true,
            item: state.modules.team?.item || [],
            name: state.modules.team?.name || '团队经历',
            support_ai: true,
            type: 'team'
          };

          // 转换其他信息
          const other = {
            id: 'other',
            index: state.modules.other?.index || 10,
            is_visible: state.modules.other?.is_visible ?? true,
            item: state.modules.other?.item || [],
            name: state.modules.other?.name || '其他信息',
            support_ai: true,
            type: 'other'
          };

          // 转换自定义模块
          const custom_modules = state.customModules.map(cm => ({
            id: cm.id,
            index: cm.index,
            items: cm.items,
            name: cm.name
          }));

          // 转换slogan
          const slogan = state.slogan || {
            slogan: { label: '', value: '' },
            title: { label: '', value: '' }
          };

          // 获取简历样式数据
          const resumeStyleState = useResumeStyleStore.getState();
          const resume_style = {
            avatar_layout: resumeStyleState.avatar_layout,
            badge_layout: resumeStyleState.badge_layout,
            base_info: resumeStyleState.base_info,
            can_change_avatar_layout: resumeStyleState.can_change_avatar_layout,
            can_change_background_style: resumeStyleState.can_change_background_style,
            can_change_base_info: resumeStyleState.can_change_base_info,
            can_change_color: resumeStyleState.can_change_color,
            can_change_date_align: resumeStyleState.can_change_date_align,
            can_change_date_format: resumeStyleState.can_change_date_format,
            can_change_font_family: resumeStyleState.can_change_font_family,
            can_change_font_gray: resumeStyleState.can_change_font_gray,
            can_change_font_size: resumeStyleState.can_change_font_size,
            can_change_header_layout: resumeStyleState.can_change_header_layout,
            can_change_line_spacing: resumeStyleState.can_change_line_spacing,
            can_change_module_spacing: resumeStyleState.can_change_module_spacing,
            can_change_page_margin: resumeStyleState.can_change_page_margin,
            can_change_title_align: resumeStyleState.can_change_title_align,
            can_change_title_style: resumeStyleState.can_change_title_style,
            can_change_skills_three_columns: resumeStyleState.can_change_skills_three_columns,
            color: resumeStyleState.color,
            color_count: resumeStyleState.color_count,
            date_align: resumeStyleState.date_align,
            date_format: resumeStyleState.date_format,
            font_family: resumeStyleState.font_family,
            font_gray: resumeStyleState.font_gray,
            font_size: resumeStyleState.font_size,
            header_layout: resumeStyleState.header_layout,
            layout_mode: resumeStyleState.layout_mode,
            left_box_width: resumeStyleState.left_box_width,
            line_spacing: resumeStyleState.line_spacing,
            module_spacing: resumeStyleState.module_spacing,
            page_margin: resumeStyleState.page_margin,
            paper_style: resumeStyleState.paper_style,
            preset_colors_dual: resumeStyleState.preset_colors_dual,
            preset_colors_single: resumeStyleState.preset_colors_single,
            resume_color: resumeStyleState.resume_color,
            resume_color2: resumeStyleState.resume_color2,
            separator: resumeStyleState.separator,
            title_align: resumeStyleState.title_align,
            title_color: resumeStyleState.title_color,
            title_row: resumeStyleState.title_row,
            title_style: resumeStyleState.title_style,
          };

          return {
            basic_info,
            education,
            work,
            project,
            skills,
            honors,
            personal_summary,
            portfolio,
            research,
            team,
            other,
            custom_modules,
            slogan,
            resume_style
          };
        };

        try {
          const saveData = convertStoreDataToApiFormat();
          const completionRate = calculateCompletenessScore();

          // 添加完整度评分字段
          const saveDataWithCompletion = {
            ...saveData,
            completion_rate: completionRate
          };

          await resumeApi.saveResume(state.resumeId.toString(), saveDataWithCompletion as unknown as Omit<SaveResumeRequest, 'resume_id'>);

          // 更新最后保存时间
          set({ lastUpdatedAt: Date.now() });

          return Promise.resolve();
        } catch (error) {
          throw error;
        }
      },


    }),
);
